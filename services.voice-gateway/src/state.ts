import type { Router, WebRtcTransport, Producer, Consumer } from 'mediasoup/node/lib/types';
import { MediaServerState } from './types';

/**
 * In-Memory State Management
 * These maps will store the state of our media server.
 * In a distributed setup, this state might be moved to a shared store like Redis.
 */

/**
 * A map of callId to its mediasoup Router.
 * Each call gets its own router to isolate its media streams.
 */
export const callRouters = new Map<string, Router>();

/**
 * A map of transportId to its WebRtcTransport instance.
 */
export const transports = new Map<string, WebRtcTransport>();

/**
 * A map of producerId to its Producer instance.
 */
export const producers = new Map<string, Producer>();

/**
 * A map of consumerId to its Consumer instance.
 */
export const consumers = new Map<string, Consumer>();

/**
 * Get all state maps as a single object
 */
export function getMediaServerState(): MediaServerState {
  return {
    callRouters,
    transports,
    producers,
    consumers,
  };
}

/**
 * Clean up resources for a specific call
 */
export function cleanupCall(callId: string): void {
  const router = callRouters.get(callId);
  if (router) {
    router.close();
    callRouters.delete(callId);
    console.log(`[${callId}] Call resources cleaned up`);
  }
}

/**
 * Clean up a specific transport and its associated resources
 */
export function cleanupTransport(transportId: string): void {
  const transport = transports.get(transportId);
  if (transport) {
    transport.close();
    transports.delete(transportId);
    console.log(`Transport ${transportId} cleaned up`);
  }
}

/**
 * Clean up a specific producer
 */
export function cleanupProducer(producerId: string): void {
  const producer = producers.get(producerId);
  if (producer) {
    producer.close();
    producers.delete(producerId);
    console.log(`Producer ${producerId} cleaned up`);
  }
}

/**
 * Clean up a specific consumer
 */
export function cleanupConsumer(consumerId: string): void {
  const consumer = consumers.get(consumerId);
  if (consumer) {
    consumer.close();
    consumers.delete(consumerId);
    console.log(`Consumer ${consumerId} cleaned up`);
  }
}
