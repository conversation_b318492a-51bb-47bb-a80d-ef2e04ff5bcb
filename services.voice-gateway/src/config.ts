import os from 'os';
import type { WorkerLogTag, WorkerLogLevel, RouterRtpCodecCapability } from 'mediasoup/node/lib/types';

export const config = {
  listenIp: '0.0.0.0',
  listenPort: 5585,

  // Mediasoup configuration
  mediasoup: {
    // Number of mediasoup workers to launch.
    // A good rule of thumb is one worker per CPU core.
    numWorkers: os.cpus().length,
    workerSettings: {
      logLevel: 'warn' as WorkerLogLevel,
      logTags: [
        'info',
        'ice',
        'dtls',
        'rtp',
        'srtp',
        'rtcp',
      ] as WorkerLogTag[],
      rtcMinPort: 40000,
      rtcMaxPort: 49999,
    },
    router: {
      // RTP media codecs supported by the server.
      mediaCodecs: [
        {
          kind: 'audio',
          mimeType: 'audio/opus',
          clockRate: 48000,
          channels: 2,
        },
      ] as RouterRtpCodecCapability[],
    },
    // WebRTC transport settings
    webRtcTransport: {
      listenIps: [
        {
          ip: process.env.WEBRTC_LISTEN_IP || '127.0.0.1',
          announcedIp: process.env.WEBRTC_ANNOUNCED_IP || undefined,
        },
      ],
      enableUdp: true,
      enableTcp: true,
      preferUdp: true,
      initialAvailableOutgoingBitrate: 1000000,
    },
  },
};
