import type { Router, WebRtcTransport, Producer, Consumer } from 'mediasoup/node/lib/types';

// Socket.IO event interfaces
export interface CreateWebRtcTransportResponse {
  id: string;
  iceParameters: any;
  iceCandidates: any;
  dtlsParameters: any;
}

export interface CreateWebRtcTransportErrorResponse {
  error: string;
}

export interface ConnectTransportData {
  transportId: string;
  dtlsParameters: any;
}

export interface ProduceData {
  transportId: string;
  kind: 'audio' | 'video';
  rtpParameters: any;
}

export interface ProduceResponse {
  id: string;
}

export interface ConsumeData {
  transportId: string;
  producerId: string;
  rtpCapabilities: any;
}

export interface ConsumeResponse {
  id: string;
  producerId: string;
  kind: string;
  rtpParameters: any;
}

export interface ConsumeErrorResponse {
  error: string;
}

export interface ResumeConsumerData {
  consumerId: string;
}

export interface NewProducerEvent {
  producerId: string;
  producerUserId: string;
}

export interface ConsumerClosedEvent {
  consumerId: string;
}

// State management interfaces
export interface MediaServerState {
  callRouters: Map<string, Router>;
  transports: Map<string, WebRtcTransport>;
  producers: Map<string, Producer>;
  consumers: Map<string, Consumer>;
}

// Socket handshake query interface
export interface SocketHandshakeQuery {
  callId: string;
  userId: string;
}
