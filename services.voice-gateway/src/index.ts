import express from 'express';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { config } from './config';
import { runMediasoupWorkers } from './mediasoup-service';
import { registerSocketHandlers } from './socket-handlers';

/**
 * Voice Gateway Server
 * 
 * This is the main entry point for the voice gateway service.
 * It sets up the Express app, Socket.IO server, and mediasoup workers.
 */

// Express App and Socket.IO Server
const app = express();
app.use(express.json());

const httpServer = http.createServer(app);
const io = new SocketIOServer(httpServer, {
  cors: {
    origin: "*", // Adjust for production
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'voice-gateway'
  });
});

// Socket.IO Connection Handling
io.on('connection', (socket) => {
  registerSocketHandlers(socket);
});

/**
 * Main Server Function
 */
async function run(): Promise<void> {
  try {
    // 1. Start Mediasoup Workers
    await runMediasoupWorkers();

    // 2. Start the HTTP/Socket.IO server
    httpServer.listen(config.listenPort, config.listenIp, () => {
      console.log(`Voice Gateway Server is running and listening on ${config.listenIp}:${config.listenPort}`);
      console.log(`Health check available at http://${config.listenIp}:${config.listenPort}/health`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  httpServer.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  httpServer.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

// Start the server
run();
