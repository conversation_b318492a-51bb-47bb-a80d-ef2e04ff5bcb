import { Socket } from 'socket.io';
import type { WebRtcTransport } from 'mediasoup/node/lib/types';
import { config } from './config';
import { getOrCreateRouter, getWebRtcServer } from './mediasoup-service';
import { transports, producers, consumers } from './state';
import {
  SocketHandshakeQuery,
  CreateWebRtcTransportResponse,
  CreateWebRtcTransportErrorResponse,
  ConnectTransportData,
  ProduceData,
  ProduceResponse,
  ConsumeData,
  ConsumeResponse,
  ConsumeErrorResponse,
  ResumeConsumerData,
  NewProducerEvent,
  ConsumerClosedEvent,
} from './types';

/**
 * Register all socket event handlers for a connected client
 */
export function registerSocketHandlers(socket: Socket): void {
  const { callId, userId } = socket.handshake.query as unknown as SocketHandshakeQuery;
  console.log(`[${callId}] Client ${userId} connected via Socket.IO with id ${socket.id}`);

  // Handle client disconnection
  socket.on('disconnect', () => {
    console.log(`[${callId}] Client ${userId} disconnected`);
    // Here you would clean up transports, producers, etc. associated with this socket.
    // This could be enhanced to track socket-specific resources
  });

  // Client asks for the server's RTP capabilities
  socket.on('getRouterRtpCapabilities', async (callback) => {
    try {
      const router = await getOrCreateRouter(callId);
      callback(router.rtpCapabilities);
    } catch (error) {
      console.error('Error getting router RTP capabilities:', error);
      callback({ error: (error as Error).message });
    }
  });

  // Client wants to create a transport to send/receive media
  socket.on('createWebRtcTransport', async (callback) => {
    try {
      const router = await getOrCreateRouter(callId);
      const webRtcServer = getWebRtcServer();
      const transport = await router.createWebRtcTransport({
        webRtcServer,
        enableUdp: true,
        enableTcp: true,
        preferUdp: true,
      });
      
      transports.set(transport.id, transport);
      console.log(`[${callId}] Created WebRTC transport ${transport.id}`);
      
      transport.on('dtlsstatechange', (dtlsState) => {
        if (dtlsState === 'closed') {
          console.log(`[${callId}] Transport ${transport.id} closed`);
          transports.delete(transport.id);
        }
      });

      const response: CreateWebRtcTransportResponse = {
        id: transport.id,
        iceParameters: transport.iceParameters,
        iceCandidates: transport.iceCandidates,
        dtlsParameters: transport.dtlsParameters,
      };
      callback(response);
    } catch (error) {
      console.error('Failed to create WebRTC transport:', error);
      const errorResponse: CreateWebRtcTransportErrorResponse = {
        error: (error as Error).message
      };
      callback(errorResponse);
    }
  });

  // Client provides its transport parameters after creating it locally
  socket.on('connectTransport', async (data: ConnectTransportData, callback) => {
    const { transportId, dtlsParameters } = data;
    const transport = transports.get(transportId);
    if (!transport) {
      console.error(`Transport with id "${transportId}" not found`);
      return callback({ error: 'Transport not found' });
    }
    
    try {
      await transport.connect({ dtlsParameters });
      callback();
    } catch (error) {
      console.error('Error connecting transport:', error);
      callback({ error: (error as Error).message });
    }
  });

  // Client wants to start sending (producing) an audio track
  socket.on('produce', async (data: ProduceData, callback) => {
    const { transportId, kind, rtpParameters } = data;
    const transport = transports.get(transportId);
    if (!transport) {
      console.error(`Transport with id "${transportId}" not found for producing`);
      return callback({ error: 'Transport not found' });
    }

    try {
      const producer = await transport.produce({
        kind: kind as 'audio' | 'video',
        rtpParameters
      });
      producers.set(producer.id, producer);

      console.log(`[${callId}] User ${userId} created producer ${producer.id}`);

      // Inform other clients in the call that a new producer is available
      const newProducerEvent: NewProducerEvent = {
        producerId: producer.id,
        producerUserId: userId
      };
      socket.to(callId).emit('newProducer', newProducerEvent);

      producer.on('transportclose', () => {
        console.log(`Producer ${producer.id} transport closed`);
        producers.delete(producer.id);
      });

      const response: ProduceResponse = { id: producer.id };
      callback(response);
    } catch (error) {
      console.error('Error creating producer:', error);
      callback({ error: (error as Error).message });
    }
  });
  
  // Client wants to start receiving (consuming) a track from another user
  socket.on('consume', async (data: ConsumeData, callback) => {
    const { transportId, producerId, rtpCapabilities } = data;
    
    try {
      const router = await getOrCreateRouter(callId);
      const transport = transports.get(transportId) as WebRtcTransport;
      
      if (!transport) {
        const errorResponse: ConsumeErrorResponse = { error: `Transport ${transportId} not found` };
        return callback(errorResponse);
      }
      if (!producers.has(producerId)) {
        const errorResponse: ConsumeErrorResponse = { error: `Producer ${producerId} not found` };
        return callback(errorResponse);
      }
      if (!router.canConsume({ producerId, rtpCapabilities })) {
        const errorResponse: ConsumeErrorResponse = { error: 'Cannot consume this producer' };
        return callback(errorResponse);
      }

      const consumer = await transport.consume({
        producerId,
        rtpCapabilities,
        paused: true // Start paused and resume on the client
      });
      consumers.set(consumer.id, consumer);

      consumer.on('transportclose', () => {
        consumers.delete(consumer.id);
      });
      consumer.on('producerclose', () => {
        consumers.delete(consumer.id);
        // Inform the client that this consumer is now closed
        const consumerClosedEvent: ConsumerClosedEvent = { consumerId: consumer.id };
        socket.emit('consumerClosed', consumerClosedEvent);
      });

      const response: ConsumeResponse = {
        id: consumer.id,
        producerId,
        kind: consumer.kind,
        rtpParameters: consumer.rtpParameters,
      };
      callback(response);
    } catch (error) {
      console.error('Consume error:', error);
      const errorResponse: ConsumeErrorResponse = { error: (error as Error).message };
      callback(errorResponse);
    }
  });

  // Client is ready to start receiving data for a consumer
  socket.on('resumeConsumer', async (data: ResumeConsumerData, callback) => {
    const { consumerId } = data;
    const consumer = consumers.get(consumerId);
    if (consumer) {
      try {
        await consumer.resume();
        callback();
      } catch (error) {
        console.error('Error resuming consumer:', error);
        callback({ error: (error as Error).message });
      }
    } else {
      callback({ error: 'Consumer not found' });
    }
  });

  // Client joins a "room" for the call to receive broadcasted events
  socket.join(callId);
}
