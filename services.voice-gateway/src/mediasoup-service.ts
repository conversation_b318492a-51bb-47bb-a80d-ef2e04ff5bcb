import * as mediasoup from 'mediasoup';
import type { Worker, Router, WebRtcServer } from 'mediasoup/node/lib/types';
import { config } from './config';
import { callRouters } from './state';

// Global Mediasoup Resources
let workers: Worker[] = [];
let webRtcServers: WebRtcServer[] = [];
let nextWorkerIdx = 0;

/**
 * Launches the mediasoup workers.
 */
export async function runMediasoupWorkers(): Promise<void> {
  console.log('Starting mediasoup workers...');
  for (let i = 0; i < config.mediasoup.numWorkers; i++) {
    const worker = await mediasoup.createWorker({
      logLevel: config.mediasoup.workerSettings.logLevel,
      logTags: config.mediasoup.workerSettings.logTags,
      rtcMinPort: config.mediasoup.workerSettings.rtcMinPort,
      rtcMaxPort: config.mediasoup.workerSettings.rtcMaxPort,
    });

    worker.on('died', () => {
      console.error(`mediasoup worker ${worker.pid} has died`);
      setTimeout(() => process.exit(1), 2000); // exit in 2 seconds
    });

    // Create WebRTC server for this worker
    const webRtcServer = await worker.createWebRtcServer({
      listenInfos: config.mediasoup.webRtcTransport.listenIps.map(listenIp => ({
        protocol: 'udp' as const,
        ip: listenIp.ip,
        ...(listenIp.announcedIp && { announcedIp: listenIp.announcedIp }),
      })),
    });

    workers.push(worker);
    webRtcServers.push(webRtcServer);
    console.log(`mediasoup worker ${worker.pid} started`);
  }
}

/**
 * Gets the next available worker in a round-robin fashion.
 */
export function getMediasoupWorker(): Worker {
  if (workers.length === 0) {
    throw new Error('No mediasoup workers available');
  }
  const worker = workers[nextWorkerIdx];
  if (!worker) {
    throw new Error('Worker not found at index');
  }
  nextWorkerIdx = (nextWorkerIdx + 1) % workers.length;
  return worker;
}

/**
 * Gets the WebRTC server for the current worker
 */
export function getWebRtcServer(): WebRtcServer {
  if (webRtcServers.length === 0) {
    throw new Error('No WebRTC servers available');
  }
  const server = webRtcServers[nextWorkerIdx === 0 ? webRtcServers.length - 1 : nextWorkerIdx - 1];
  if (!server) {
    throw new Error('WebRTC server not found');
  }
  return server;
}

/**
 * Gets or creates a mediasoup Router for a given callId.
 */
export async function getOrCreateRouter(callId: string): Promise<Router> {
  if (callRouters.has(callId)) {
    return callRouters.get(callId)!;
  }

  const worker = getMediasoupWorker();
  const router = await worker.createRouter({ 
    mediaCodecs: config.mediasoup.router.mediaCodecs 
  });
  
  callRouters.set(callId, router);
  console.log(`[${callId}] Created new mediasoup Router`);

  // Clean up the router when it's closed (e.g., call ends)
  router.on('@close', () => {
    callRouters.delete(callId);
    console.log(`[${callId}] Mediasoup Router closed and cleaned up.`);
  });

  return router;
}

/**
 * Get the number of active workers
 */
export function getWorkerCount(): number {
  return workers.length;
}

/**
 * Get worker statistics
 */
export function getWorkerStats() {
  return workers.map((worker, index) => ({
    index,
    pid: worker.pid,
    closed: worker.closed,
  }));
}
