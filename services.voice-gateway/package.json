{"name": "cortexa-voice-gateway", "version": "1.0.0", "description": "SFU for WebRTC voice-gateway service", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node server.ts", "watch": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"ws": "^8.14.2", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/node": "^20.8.0", "typescript": "^5.2.2", "ts-node": "^10.9.1"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "ISC"}