
import express from 'express';
import http from 'http';
import https from 'https.js';
import { Server as SocketIOServer } from 'socket.io';
import * as mediasoup from 'mediasoup';
import { Worker } from 'mediasoup/node/lib/Worker';
import { Router } from 'mediasoup/node/lib/Router';
import { WebRtcTransport } from 'mediasoup/node/lib/WebRtcTransport';
import { Producer } from 'mediasoup/node/lib/Producer';
import { Consumer } from 'mediasoup/node/lib/Consumer';
import { RtpCapabilities } from 'mediasoup/node/lib/RtpParameters';
import os from 'os';

// --- Configuration ---
// These should be configured via environment variables in a real deployment.
const config = {
  // Server listening IPs
  listenIp: '0.0.0.0',
  listenPort: 3000,

  // Mediasoup configuration
  mediasoup: {
    // Number of mediasoup workers to launch.
    // A good rule of thumb is one worker per CPU core.
    numWorkers: os.cpus().length,
    workerSettings: {
      logLevel: 'warn',
      logTags: [
        'info',
        'ice',
        'dtls',
        'rtp',
        'srtp',
        'rtcp',
      ],
      rtcMinPort: 40000,
      rtcMaxPort: 49999,
    },
    router: {
      // RTP media codecs supported by the server.
      mediaCodecs: [
        {
          kind: 'audio',
          mimeType: 'audio/opus',
          clockRate: 48000,
          channels: 2,
        },
      ],
    },
    // WebRTC transport settings
    webRtcTransport: {
      listenIps: [
        {
          ip: process.env.WEBRTC_LISTEN_IP || '127.0.0.1',
          announcedIp: process.env.WEBRTC_ANNOUNCED_IP || undefined,
        },
      ],
      enableUdp: true,
      enableTcp: true,
      preferUdp: true,
      initialAvailableOutgoingBitrate: 1000000,
    },
  },
} as const;


// --- In-Memory State Management ---
// These maps will store the state of our media server.
// In a distributed setup, this state might be moved to a shared store like Redis.

/**
 * A map of callId to its mediasoup Router.
 * Each call gets its own router to isolate its media streams.
 */
const callRouters = new Map<string, Router>();

/**
 * A map of transportId to its WebRtcTransport instance.
 */
const transports = new Map<string, WebRtcTransport>();

/**
 * A map of producerId to its Producer instance.
 */
const producers = new Map<string, Producer>();

/**
 * A map of consumerId to its Consumer instance.
 */
const consumers = new Map<string, Consumer>();


// --- Global Mediasoup Resources ---
let workers: Worker[] = [];
let nextWorkerIdx = 0;


// --- Express App and Socket.IO Server ---
const app = express();
app.use(express.json());
const httpServer = http.createServer(app);
const io = new SocketIOServer(httpServer, {
  cors: {
    origin: "*", // Adjust for production
  }
});

// --- Core Mediasoup Functions ---

/**
 * Launches the mediasoup workers.
 */
async function runMediasoupWorkers() {
  console.log('Starting mediasoup workers...');
  for (let i = 0; i < config.mediasoup.numWorkers; i++) {
    const worker = await mediasoup.createWorker({
      logLevel: config.mediasoup.workerSettings.logLevel,
      logTags: config.mediasoup.workerSettings.logTags,
      rtcMinPort: config.mediasoup.workerSettings.rtcMinPort,
      rtcMaxPort: config.mediasoup.workerSettings.rtcMaxPort,
    });

    worker.on('died', () => {
      console.error(`mediasoup worker ${worker.pid} has died`);
      setTimeout(() => process.exit(1), 2000); // exit in 2 seconds
    });

    workers.push(worker);
    console.log(`mediasoup worker ${worker.pid} started`);
  }
}

/**
 * Gets the next available worker in a round-robin fashion.
 */
function getMediasoupWorker() {
  const worker = workers[nextWorkerIdx];
  nextWorkerIdx = (nextWorkerIdx + 1) % workers.length;
  return worker;
}

/**
 * Gets or creates a mediasoup Router for a given callId.
 */
async function getOrCreateRouter(callId: string): Promise<Router> {
    if (callRouters.has(callId)) {
        return callRouters.get(callId)!;
    }

    const worker = getMediasoupWorker();
    const router = await worker.createRouter({ mediaCodecs: config.mediasoup.router.mediaCodecs });
    
    callRouters.set(callId, router);
    console.log(`[${callId}] Created new mediasoup Router`);

    // Clean up the router when it's closed (e.g., call ends)
    router.on('close', () => {
        callRouters.delete(callId);
        console.log(`[${callId}] Mediasoup Router closed and cleaned up.`);
    });

    return router;
}


// --- Socket.IO Connection Handling ---
// This is the primary interface for clients (browsers) to interact with the media server.
// It replaces the need for a separate Signaling Service for media-related signaling.
io.on('connection', (socket) => {
  const { callId, userId } = socket.handshake.query;
  console.log(`[${callId}] Client ${userId} connected via Socket.IO with id ${socket.id}`);

  socket.on('disconnect', () => {
    console.log(`[${callId}] Client ${userId} disconnected`);
    // Here you would clean up transports, producers, etc. associated with this socket.
  });

  // --- Mediasoup Signaling Handlers ---

  // Client asks for the server's RTP capabilities
  socket.on('getRouterRtpCapabilities', async (callback) => {
    const router = await getOrCreateRouter(callId as string);
    callback(router.rtpCapabilities);
  });

  // Client wants to create a transport to send/receive media
  socket.on('createWebRtcTransport', async (callback) => {
    try {
      const router = await getOrCreateRouter(callId as string);
      const transport = await router.createWebRtcTransport(config.mediasoup.webRtcTransport);
      
      transports.set(transport.id, transport);
      console.log(`[${callId}] Created WebRTC transport ${transport.id}`);
      
      transport.on('dtlsstatechange', (dtlsState) => {
        if (dtlsState === 'closed') {
          console.log(`[${callId}] Transport ${transport.id} closed`);
          transports.delete(transport.id);
        }
      });

      callback({
        id: transport.id,
        iceParameters: transport.iceParameters,
        iceCandidates: transport.iceCandidates,
        dtlsParameters: transport.dtlsParameters,
      });
    } catch (error) {
      console.error('Failed to create WebRTC transport:', error);
      callback({ error: (error as Error).message });
    }
  });

  // Client provides its transport parameters after creating it locally
  socket.on('connectTransport', async ({ transportId, dtlsParameters }, callback) => {
    const transport = transports.get(transportId);
    if (!transport) {
      console.error(`Transport with id "${transportId}" not found`);
      return callback({ error: 'Transport not found' });
    }
    await transport.connect({ dtlsParameters });
    callback();
  });

  // Client wants to start sending (producing) an audio track
  socket.on('produce', async ({ transportId, kind, rtpParameters }, callback) => {
    const transport = transports.get(transportId);
    if (!transport) {
      console.error(`Transport with id "${transportId}" not found for producing`);
      return callback({ error: 'Transport not found' });
    }

    const producer = await transport.produce({ kind, rtpParameters });
    producers.set(producer.id, producer);

    console.log(`[${callId}] User ${userId} created producer ${producer.id}`);

    // Inform other clients in the call that a new producer is available
    socket.to(callId as string).emit('newProducer', { producerId: producer.id, producerUserId: userId });

    producer.on('transportclose', () => {
      console.log(`Producer ${producer.id} transport closed`);
      producers.delete(producer.id);
    });

    callback({ id: producer.id });
  });
  
  // Client wants to start receiving (consuming) a track from another user
  socket.on('consume', async ({ transportId, producerId, rtpCapabilities }, callback) => {
    const router = await getOrCreateRouter(callId as string);
    const transport = transports.get(transportId) as WebRtcTransport;
    
    if (!transport) {
        return callback({ error: `Transport ${transportId} not found` });
    }
    if (!producers.has(producerId)) {
        return callback({ error: `Producer ${producerId} not found` });
    }
    if (!router.canConsume({ producerId, rtpCapabilities })) {
        return callback({ error: 'Cannot consume this producer' });
    }

    try {
        const consumer = await transport.consume({
            producerId,
            rtpCapabilities,
            paused: true // Start paused and resume on the client
        });
        consumers.set(consumer.id, consumer);

        consumer.on('transportclose', () => {
            consumers.delete(consumer.id);
        });
        consumer.on('producerclose', () => {
            consumers.delete(consumer.id);
            // Optionally, inform the client that this consumer is now closed
            socket.emit('consumerClosed', { consumerId: consumer.id });
        });

        callback({
            id: consumer.id,
            producerId,
            kind: consumer.kind,
            rtpParameters: consumer.rtpParameters,
        });
    } catch (error) {
        console.error('Consume error:', error);
        callback({ error: (error as Error).message });
    }
  });

  // Client is ready to start receiving data for a consumer
  socket.on('resumeConsumer', async ({ consumerId }, callback) => {
    const consumer = consumers.get(consumerId);
    if (consumer) {
        await consumer.resume();
    }
    callback();
  });

  // Client joins a "room" for the call to receive broadcasted events
  socket.join(callId as string);
});


// --- Main Server Function ---
async function run() {
  // 1. Start Mediasoup Workers
  await runMediasoupWorkers();

  // 2. Start the HTTP/Socket.IO server
  httpServer.listen(config.listenPort, config.listenIp, () => {
    console.log(`Media Server is running and listening on ${config.listenIp}:${config.listenPort}`);
  });
}

run();