import { WebSocketServer, WebSocket } from 'ws';
import http from 'http';
import url from 'url';
import jwt from 'jsonwebtoken';

const PORT = process.env.PORT || 8080;
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-and-long-key';

interface AuthPayload {
  userId: string;
  callId: string;
  // Add other claims like 'iat', 'exp' as needed by your token generation.
}

const callSessions = new Map<string, Set<WebSocket>>();

// --- Server Setup ---
const server = http.createServer((req, res) => {
  res.writeHead(404);
  res.end();
});

const wss = new WebSocketServer({ server });

// --- WebSocket Server Logic ---
wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
  let callId: string | null = null;
  let userId: string | null = null;

  try {
    // 1. Authentication and Authorization
    // The WebSocket connection URL must include the JWT and callId.
    // e.g., wss://your-domain.com?token=...&callId=...
    const parameters = url.parse(req.url || '', true).query;
    const token = parameters.token as string;
    const providedCallId = parameters.callId as string;

    if (!token ||!providedCallId) {
      throw new Error('Missing token or callId query parameter.');
    }

    // Verify the JWT. This will throw an error if the token is invalid or expired.
    const decoded = jwt.verify(token, JWT_SECRET) as AuthPayload;

    // Perform authorization checks
    if (decoded.callId!== providedCallId) {
        throw new Error('JWT is not valid for the requested call session.');
    }

    callId = decoded.callId;
    userId = decoded.userId;

    // 2. Add Client to Session
    if (!callSessions.has(callId)) {
      callSessions.set(callId, new Set());
    }
    const session = callSessions.get(callId)!;
    session.add(ws);

    console.log(`[${callId}] Client ${userId} connected. Session size: ${session.size}`);

  } catch (err) {
    // If authentication or setup fails, terminate the connection immediately.
    console.error('Authentication failed:', (err as Error).message);
    ws.terminate();
    return;
  }

  // 3. Message Relaying
  ws.on('message', (message: Buffer) => {
    // The message is relayed to all *other* clients in the same call session.
    // The signaling server does not need to understand the content of the message.
    // It just acts as a broker for SDP offers/answers and ICE candidates.
    const session = callSessions.get(callId!);
    if (session) {
      session.forEach(client => {
        if (client!== ws && client.readyState === WebSocket.OPEN) {
          client.send(message);
        }
      });
    }
  });

  // 4. Handle Client Disconnection
  ws.on('close', () => {
    if (callId) {
      const session = callSessions.get(callId);
      if (session) {
        session.delete(ws);
        console.log(`[${callId}] Client ${userId} disconnected. Session size: ${session.size}`);

        // If the session is now empty, clean it up from the map.
        if (session.size === 0) {
          callSessions.delete(callId);
          console.log(`[${callId}] Session closed.`);
        }
      }
    }
  });

  ws.on('error', (error) => {
    console.error(`[${callId}] WebSocket error for client ${userId}:`, error);
  });
});

// --- Start Server ---
server.listen(PORT, () => {
  console.log(`Signaling Server started on port ${PORT}`);
});
