{"name": "yn", "version": "3.1.1", "description": "Parse yes/no like values", "license": "MIT", "repository": "sindresorhus/yn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "lenient.js", "index.d.ts"], "keywords": ["yn", "yes", "no", "cli", "prompt", "validate", "input", "answer", "true", "false", "parse", "lenient"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}